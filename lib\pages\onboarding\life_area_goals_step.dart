import 'package:flutter/material.dart';
import '../../widgets/onboarding/onboarding_step_base.dart';
import '../../widgets/onboarding/coach_chip.dart';
import '../../theme/theme.dart';

class LifeAreaGoalsStep extends StatefulWidget {
  final String areaName;
  final List<String> goalOptions; // without the satisfied option
  final void Function(List<String> goals) onGoalsChanged;
  final VoidCallback? onBack;
  final VoidCallback? onSkip;
  final VoidCallback? onContinue;
  final List<String>? initialGoals;
  final int progressCurrent;
  final int progressTotal;

  const LifeAreaGoalsStep({
    super.key,
    required this.areaName,
    required this.goalOptions,
    required this.onGoalsChanged,
    this.onBack,
    this.onSkip,
    this.onContinue,
    this.initialGoals,
    required this.progressCurrent,
    required this.progressTotal,
  });

  @override
  State<LifeAreaGoalsStep> createState() => _LifeAreaGoalsStepState();
}

class _LifeAreaGoalsStepState extends State<LifeAreaGoalsStep> {
  static const String satisfiedOption = "I'm satisfied with this area";

  late List<String> _selected;

  @override
  void initState() {
    super.initState();
    _selected = List.from(widget.initialGoals ?? <String>[]);
  }

  void _toggle(String goal) {
    setState(() {
      if (goal == satisfiedOption) {
        if (_selected.contains(satisfiedOption)) {
          _selected.remove(satisfiedOption);
        } else {
          _selected
            ..clear()
            ..add(satisfiedOption);
        }
      } else {
        _selected.remove(satisfiedOption);
        if (_selected.contains(goal)) {
          _selected.remove(goal);
        } else {
          _selected.add(goal);
        }
      }
    });
    widget.onGoalsChanged(List.from(_selected));
  }

  bool get _canContinue => _selected.isNotEmpty;

  @override
  Widget build(BuildContext context) {
    final allOptions = [...widget.goalOptions, satisfiedOption];

    return OnboardingStepBase(
      currentStep: widget.progressCurrent,
      totalSteps: widget.progressTotal,
      title: widget.areaName,
      subtitle: 'Choose goals to improve this area',
      onBack: widget.onBack,
      onSkip: null,
      onContinue: _canContinue ? widget.onContinue : null,
      canContinue: _canContinue,
      child: ListView.builder(
        itemCount: allOptions.length,
        itemBuilder: (context, index) {
          final goal = allOptions[index];
          final isSelected = _selected.contains(goal);
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: CoachChip(
              text: goal,
              isSelected: isSelected,
              fullWidth: true,
              onTap: () => _toggle(goal),
            ),
          );
        },
      ),
    );
  }
}
