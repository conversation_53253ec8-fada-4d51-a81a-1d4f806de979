import 'dart:convert';
import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/models/models.dart';

void main() {
  group('chat_subjects.json validation', () {
    late List<dynamic> jsonData;

    setUpAll(() {
      final file = File('data/chat_subjects.json');
      expect(
        file.existsSync(),
        isTrue,
        reason: 'data/chat_subjects.json must exist',
      );
      final contents = file.readAsStringSync();
      jsonData = jsonDecode(contents) as List<dynamic>;
    });

    test('contains 20 subjects', () {
      expect(jsonData.length, 20);
    });

    test('matches ChatSubject model schema', () {
      for (final item in jsonData) {
        expect(item, isA<Map<String, dynamic>>());
        final map = Map<String, dynamic>.from(item as Map);

        // Required fields presence
        expect(map.containsKey('name'), isTrue);
        expect(map.containsKey('description'), isTrue);
        expect(map.containsKey('prompt'), isTrue);
        expect(map.containsKey('isActive'), isTrue);
        expect(map.containsKey('defaultOrder'), isTrue);
        expect(map.containsKey('category'), isTrue);

        // Parse via model (unknown fields like 'category' are ignored by model)
        final subject = ChatSubject.fromJson(map);

        // Field validations
        expect(subject.name.isNotEmpty, isTrue);
        expect(subject.description.isNotEmpty, isTrue);
        expect(subject.prompt.isNotEmpty, isTrue);
        expect(subject.defaultOrder >= 0, isTrue);

        // Category basic validation
        final category = map['category'];
        expect(category, isA<String>());
        expect((category as String).isNotEmpty, isTrue);
      }
    });
  });
}
