import 'package:flutter/foundation.dart';
import '../models/models.dart';
import '../services/system_prompt_service.dart';

/// Utility class for building comprehensive system prompts that incorporate
/// UserProfile information and context for AI coaching conversations.
class SystemPromptBuilder {
  /// Builds a system prompt for chat with persona using configurable templates
  ///
  /// This method fetches the prompt template from SystemPromptService and injects
  /// the provided parameters (systemPersona, userProfile, conversationContext, pathStep).
  ///
  /// Parameters are injected at entity level using placeholders:
  /// - {systemPersona} - Complete persona information
  /// - {userProfile} - Complete user profile information
  /// - {conversationContext} - Current conversation context
  /// - {discussionTopic} - Dynamic topic guidance based on chat context
  static Future<String> buildChatWithPersonaSystemPrompt({
    SystemPersona? systemPersona,
    UserProfile? userProfile,
    String? conversationContext,
    UserSubscription? userSubscription,
    PathStep? pathStep,
    ChatSubject? chatSubject,
  }) async {
    try {
      // Get the prompt template from SystemPromptService
      final template = await SystemPromptService.instance
          .getChatWithPersonaPrompt(userSubscription: userSubscription);

      // Inject parameters into the template
      String prompt = template;

      // Inject system persona information
      if (systemPersona != null) {
        final personaSection = _buildPersonaSection(systemPersona);
        prompt = prompt.replaceAll('{systemPersona}', personaSection);
      } else {
        prompt = prompt.replaceAll('{systemPersona}', '');
      }

      // Inject user profile information
      if (userProfile != null) {
        final profileSection = _buildUserProfileSection(userProfile);
        prompt = prompt.replaceAll('{userProfile}', profileSection);
      } else {
        prompt = prompt.replaceAll(
          '{userProfile}',
          'No user profile information available.',
        );
      }

      // Inject conversation context
      if (conversationContext != null && conversationContext.isNotEmpty) {
        final contextSection = _buildConversationContextSection(
          conversationContext,
        );
        prompt = prompt.replaceAll('{conversationContext}', contextSection);
      } else {
        final defaultContext = _buildDefaultContextSection();
        prompt = prompt.replaceAll('{conversationContext}', defaultContext);
      }

      // Validate mutual exclusivity between PathStep and ChatSubject
      if (pathStep != null && chatSubject != null) {
        throw ArgumentError(
          'Provide only one of pathStep or chatSubject, not both.',
        );
      }

      // Inject discussion topic guidance
      final discussionTopicSection = pathStep != null
          ? buildDiscussionTopicSectionFromPathStep(pathStep)
          : chatSubject != null
          ? buildDiscussionTopicSectionFromChatSubject(chatSubject)
          : buildDefaultDiscussionTopicSection();
      prompt = prompt.replaceAll('{discussionTopic}', discussionTopicSection);

      return prompt.trim();
    } catch (e) {
      // Fallback to a basic prompt if template loading fails
      final buffer = StringBuffer();

      // Basic AI role section
      if (systemPersona != null) {
        buffer.writeln(_buildPersonaSection(systemPersona));
      } else {
        buffer.writeln('# AI Coach Role & Identity');
        buffer.writeln();
        buffer.writeln(
          'You are an AI coaching assistant designed to provide personalized guidance, support, and motivation.',
        );
      }

      buffer.writeln();
      buffer.writeln(_buildDefaultContextSection());

      if (userProfile != null) {
        buffer.writeln();
        buffer.writeln(_buildUserProfileSection(userProfile));
      }

      final fallbackPrompt = buffer.toString().trim();
      // Debug: Log the generated fallback system prompt
      debugPrint('DEBUG: SystemPromptBuilder fallback prompt: $fallbackPrompt');

      return fallbackPrompt;
    }
  }

  /// Builds persona section for template injection
  static String _buildPersonaSection(SystemPersona systemPersona) {
    final buffer = StringBuffer();

    buffer.writeln(
      'You are embodying the coaching persona: **${systemPersona.name}**',
    );

    if (systemPersona.description.isNotEmpty) {
      buffer.writeln();
      buffer.writeln('**Persona Description**: ${systemPersona.description}');
      buffer.writeln();
      buffer.writeln(
        'Embody this persona\'s unique coaching style and approach.',
      );
    }

    buffer.writeln();

    return buffer.toString().trimRight();
  }

  /// Builds conversation context section for template injection
  static String _buildConversationContextSection(String context) {
    final now = DateTime.now();
    final timeZone = now.timeZoneName;
    final formattedTime = now.toIso8601String();
    final dayOfWeek = _getDayOfWeek(now.weekday);

    return '''
- **Current Time**: $formattedTime ($timeZone)
- **Day**: $dayOfWeek
- **Session Type**: AI Coaching Conversation
- **Context**: $context''';
  }

  /// Builds default context section when no specific context is provided
  static String _buildDefaultContextSection() {
    final now = DateTime.now();
    final timeZone = now.timeZoneName;
    final formattedTime = now.toIso8601String();
    final dayOfWeek = _getDayOfWeek(now.weekday);

    return '''
- **Current Time**: $formattedTime ($timeZone)
- **Day**: $dayOfWeek
- **Session Type**: AI Coaching Conversation''';
  }

  static String _buildUserProfileSection(UserProfile userProfile) {
    final buffer = StringBuffer();

    // Basic Information
    if (userProfile.name != null ||
        userProfile.age != null ||
        userProfile.gender != null) {
      buffer.writeln('## Basic Information');
      if (userProfile.name != null) {
        buffer.writeln('- **Name**: ${userProfile.name}');
      }
      if (userProfile.age != null) {
        buffer.writeln('- **Age**: ${userProfile.age}');
      }
      if (userProfile.gender != null) {
        buffer.writeln('- **Gender**: ${userProfile.gender}');
      }
      if (userProfile.familyStatus != null) {
        buffer.writeln('- **Family Status**: ${userProfile.familyStatus}');
      }
      if (userProfile.location != null) {
        final location = userProfile.location!;
        final locationStr = location.town != null
            ? '${location.town}, ${location.country}'
            : location.country;
        buffer.writeln('- **Location**: $locationStr');
      }
      buffer.writeln();
    }

    // Family & Relationships
    if (userProfile.family != null && userProfile.family!.isNotEmpty) {
      buffer.writeln('## Family & Relationships');
      for (final relation in userProfile.family!) {
        buffer.write(
          '- **${relation.name}** (${relation.relation}, age ${relation.age})',
        );
        if (relation.otherInfo != null && relation.otherInfo!.isNotEmpty) {
          buffer.write(' - ${relation.otherInfo!.join(', ')}');
        }
        buffer.writeln();
      }
      buffer.writeln();
    }

    // Goals
    if (userProfile.goals != null && userProfile.goals!.isNotEmpty) {
      buffer.writeln('## Current Goals');
      for (final goal in userProfile.goals!) {
        buffer.writeln('- **${goal.description}** (Status: ${goal.status})');
      }
      buffer.writeln();
    }

    // Preferences & Interests
    if (userProfile.likes != null && userProfile.likes!.isNotEmpty) {
      buffer.writeln('## Interests & Likes');
      buffer.writeln('- ${userProfile.likes!.join(', ')}');
      buffer.writeln();
    }

    if (userProfile.dislikes != null && userProfile.dislikes!.isNotEmpty) {
      buffer.writeln('## Dislikes');
      buffer.writeln('- ${userProfile.dislikes!.join(', ')}');
      buffer.writeln();
    }

    // Personality Traits
    if (userProfile.personalityTraits != null &&
        userProfile.personalityTraits!.isNotEmpty) {
      buffer.writeln('## Personality Traits');
      buffer.writeln('- ${userProfile.personalityTraits!.join(', ')}');
      buffer.writeln();
    }

    // Key Facts
    if (userProfile.facts != null && userProfile.facts!.isNotEmpty) {
      buffer.writeln('## Important Facts');
      for (final fact in userProfile.facts!) {
        buffer.writeln('- **${fact.key}**: ${_formatFactValue(fact.value)}');
      }
      buffer.writeln();
    }

    // Preferences
    if (userProfile.preferences != null &&
        userProfile.preferences!.isNotEmpty) {
      buffer.writeln('## Communication Preferences');
      userProfile.preferences!.forEach((key, value) {
        buffer.writeln('- **$key**: $value');
      });
      buffer.writeln();
    }

    return buffer.toString().trimRight();
  }

  /// Safely converts a fact value (dynamic type) to a String for display
  /// Handles null, String, int, bool, and other types appropriately
  static String _formatFactValue(dynamic value) {
    if (value == null) {
      return 'Not set';
    }

    if (value is String) {
      return value.isEmpty ? 'Empty' : value;
    }

    if (value is int || value is double) {
      return value.toString();
    }

    if (value is bool) {
      return value ? 'Yes' : 'No';
    }

    // For any other type, convert to string
    return value.toString();
  }

  static String _getDayOfWeek(int weekday) {
    const days = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday',
    ];
    return days[weekday - 1];
  }

  /// Builds the discussion topic section for a PathStep-initiated chat
  static String buildDiscussionTopicSectionFromPathStep(PathStep pathStep) {
    final buffer = StringBuffer();

    buffer.writeln('## Session Focus: ${pathStep.title}');
    buffer.writeln();
    buffer.writeln('**Context:** ${pathStep.description}');
    buffer.writeln();

    if (pathStep.reflectionPrompts?.isNotEmpty == true) {
      buffer.writeln('**Key Reflection Areas:**');
      for (final prompt in pathStep.reflectionPrompts!) {
        buffer.writeln('- $prompt');
      }
      buffer.writeln();
    }

    if ((pathStep.completionCriteria).isNotEmpty) {
      buffer.writeln('**Session Goal:** ${pathStep.completionCriteria}');
      buffer.writeln();
    }

    buffer.writeln('**Coaching Instructions:**');
    buffer.writeln(
      '- Focus the conversation on helping the user work through this step',
    );
    buffer.writeln(
      '- Guide them through reflection prompts naturally during conversation',
    );
    buffer.writeln(
      '- Help them apply concepts to their personal situation with examples',
    );
    buffer.writeln(
      '- Acknowledge progress; encourage momentum and reinforce confidence',
    );

    return buffer.toString().trimRight();
  }

  /// Builds the discussion topic section for a selected ChatSubject
  static String buildDiscussionTopicSectionFromChatSubject(
    ChatSubject chatSubject,
  ) {
    final buffer = StringBuffer();

    buffer.writeln('## Session Focus: ${chatSubject.name}');
    buffer.writeln();
    buffer.writeln('**Context:** ${chatSubject.description}');
    buffer.writeln();

    buffer.writeln('**Coaching Instructions:**');
    buffer.writeln('- ${chatSubject.prompt}');
    buffer.writeln(
      '- Begin by clarifying what the user wants from this session',
    );
    buffer.writeln(
      '- Use reflective listening and 1–2 powerful, open-ended questions',
    );
    buffer.writeln(
      '- Offer a simple structure (e.g., GROW) and co-create next steps',
    );

    return buffer.toString().trimRight();
  }

  /// Builds the default discussion topic section for general chats
  static String buildDefaultDiscussionTopicSection() {
    final buffer = StringBuffer();

    buffer.writeln('## Session Focus: Personal Check-In & Growth Exploration');
    buffer.writeln();
    buffer.writeln(
      '**Context:** This is an open coaching conversation where the user can '
      'explore any aspect of their personal growth journey.',
    );
    buffer.writeln();
    buffer.writeln('**Coaching Instructions:**');
    buffer.writeln(
      '- Start with open-ended questions to understand what\'s on their mind',
    );
    buffer.writeln(
      '- Ask "What\'s on your mind?" "How can I help?" or similar prompts',
    );
    buffer.writeln(
      '- Listen actively; follow their lead with supportive guidance',
    );
    buffer.writeln('- Help identify areas for growth, reflection, or action');
    buffer.writeln(
      '- Encourage deeper self-reflection through thoughtful questions',
    );
    buffer.writeln(
      '- Provide practical insights and actionable next steps when helpful',
    );

    return buffer.toString().trimRight();
  }
}
