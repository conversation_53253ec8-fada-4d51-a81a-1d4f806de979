import 'package:flutter/material.dart';
import '../../widgets/onboarding/onboarding_step_base.dart';

import '../../theme/theme.dart';

class LifeAreaRatingStep extends StatefulWidget {
  final String areaName;
  final String areaDescription;
  final void Function(int rating) onRatingChanged;
  final VoidCallback? onBack;
  final VoidCallback? onSkip;
  final VoidCallback? onContinue;
  final int? initialRating;
  final int progressCurrent;
  final int progressTotal;

  const LifeAreaRatingStep({
    super.key,
    required this.areaName,
    required this.areaDescription,
    required this.onRatingChanged,
    this.onBack,
    this.onSkip,
    this.onContinue,
    this.initialRating,
    required this.progressCurrent,
    required this.progressTotal,
  });

  @override
  State<LifeAreaRatingStep> createState() => _LifeAreaRatingStepState();
}

class _LifeAreaRatingStepState extends State<LifeAreaRatingStep> {
  int? _rating;

  @override
  void initState() {
    super.initState();
    _rating = widget.initialRating;
  }

  void _selectRating(int value) {
    setState(() => _rating = value);
    widget.onRatingChanged(value);
  }

  @override
  Widget build(BuildContext context) {
    return OnboardingStepBase(
      currentStep: widget.progressCurrent,
      totalSteps: widget.progressTotal,
      title: widget.areaName,
      subtitle: widget.areaDescription,
      onBack: widget.onBack,
      onSkip: null,
      onContinue: _rating != null ? widget.onContinue : null,
      canContinue: _rating != null,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Rate this area (1 = needs improvement, 10 = excellent)',
            style: TextStyle(fontSize: 16, color: AppColors.textSecondary),
          ),
          const SizedBox(height: 16),
          // Gradient rating slider (1..10)
          SizedBox(
            height: 48,
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Gradient track background
                Positioned.fill(
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(999),
                    child: Container(
                      decoration: const BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: [Colors.red, Colors.yellow, Colors.green],
                          stops: [0.0, 0.55, 1.0],
                        ),
                      ),
                    ),
                  ),
                ),
                // Transparent track slider over gradient
                SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    trackHeight: 16,
                    inactiveTrackColor: Colors.transparent,
                    activeTrackColor: Colors.transparent,
                    thumbColor: Colors.white,
                    overlayColor: Theme.of(
                      context,
                    ).colorScheme.primary.withValues(alpha: 0.2),
                    thumbShape: const RoundSliderThumbShape(
                      enabledThumbRadius: 12,
                    ),
                  ),
                  child: Slider(
                    min: 1,
                    max: 10,
                    divisions: 9,
                    value: (_rating ?? 5).toDouble(),
                    label: _rating?.toString(),
                    onChanged: (v) => _selectRating(v.round()),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: const [
              Text('1', style: TextStyle(color: AppColors.textSecondary)),
              Text('10', style: TextStyle(color: AppColors.textSecondary)),
            ],
          ),
        ],
      ),
    );
  }
}
