import 'package:flutter/material.dart';
import '../../theme/theme.dart';
import '../../models/models.dart';
import '../../widgets/onboarding/onboarding_step_base.dart';
import '../../widgets/persona_selection_carousel.dart';

/// Step 5: Coach Selection
/// User selects their preferred coach from a horizontal carousel
class CoachSelectionStep extends StatefulWidget {
  final List<SystemPersona> coaches;
  final Function(SystemPersona?) onCoachSelected;
  final Function(SystemPersona)? onVideoTap;
  final VoidCallback? onBack;
  final VoidCallback? onContinue;
  final SystemPersona? initialSelectedCoach;
  final String userName;
  final int progressCurrent;
  final int progressTotal;

  const CoachSelectionStep({
    super.key,
    required this.coaches,
    required this.onCoachSelected,
    this.onVideoTap,
    this.onBack,
    this.onContinue,
    this.initialSelectedCoach,
    this.userName = '',
    this.progressCurrent = 5,
    this.progressTotal = 5,
  });

  @override
  State<CoachSelectionStep> createState() => _CoachSelectionStepState();
}

class _CoachSelectionStepState extends State<CoachSelectionStep> {
  SystemPersona? _selectedCoach;

  @override
  void initState() {
    super.initState();
    _selectedCoach = widget.initialSelectedCoach;
  }

  void _onCoachSelected(SystemPersona coach) {
    setState(() {
      _selectedCoach = coach;
    });
    widget.onCoachSelected(coach);
  }

  bool get _canContinue => _selectedCoach != null;

  @override
  Widget build(BuildContext context) {
    return OnboardingStepBase(
      currentStep: widget.progressCurrent,
      totalSteps: widget.progressTotal,
      title: 'Choose your coach',
      subtitle: widget.userName.isNotEmpty
          ? 'Hi ${widget.userName}! Pick the coaching style that resonates with you.'
          : 'Pick the coaching style that resonates with you.',
      onBack: widget.onBack,
      onContinue: _canContinue ? widget.onContinue : null,
      canContinue: _canContinue,
      child: widget.coaches.isEmpty
          ? const Center(
              child: Text(
                'No coaches available',
                style: TextStyle(color: AppColors.textSecondary, fontSize: 16),
              ),
            )
          : PersonaSelectionCarousel(
              personas: widget.coaches,
              selectedPersonaId: _selectedCoach?.id,
              onPersonaSelected: (id) {
                final coach = id == null
                    ? null
                    : widget.coaches.firstWhere(
                        (c) => c.id == id,
                        orElse: () => widget.coaches.first,
                      );
                if (coach != null) {
                  _onCoachSelected(coach);
                }
              },
              onAvatarTap: widget.onVideoTap,
              showSelectionIndicator: true,
              allowMultiSelection: false,
            ),
    );
  }
}
