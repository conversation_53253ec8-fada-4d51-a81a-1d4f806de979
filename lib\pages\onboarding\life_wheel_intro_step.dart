import 'package:flutter/material.dart';
import '../../widgets/onboarding/onboarding_step_base.dart';
import '../../theme/theme.dart';

class LifeWheelIntroStep extends StatelessWidget {
  final VoidCallback? onBack;
  final VoidCallback? onContinue;
  final int progressCurrent;
  final int progressTotal;

  const LifeWheelIntroStep({
    super.key,
    this.onBack,
    this.onContinue,
    required this.progressCurrent,
    required this.progressTotal,
  });

  @override
  Widget build(BuildContext context) {
    return OnboardingStepBase(
      currentStep: progressCurrent,
      totalSteps: progressTotal,
      title: 'Wheel of Life',
      subtitle:
          'A quick self‑assessment to see where you\'re thriving and '
          'what needs attention.',
      onBack: onBack,
      onContinue: onContinue,
      canContinue: true,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: const [
            SizedBox(height: 4),
            Text(
              'What it is',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'The Wheel of Life helps you reflect on eight key areas of '
              'life to understand balance and identify priorities.',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
                height: 1.4,
              ),
            ),
            SizedBox(height: 20),
            Text(
              'How it works',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            SizedBox(height: 8),
            Text(
              '• You\'ll rate each area from 1 (needs improvement) to '
              '10 (excellent).\n'
              '• After each rating, choose goals to improve that area, or '
              'select "I\'m satisfied with this area".',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

