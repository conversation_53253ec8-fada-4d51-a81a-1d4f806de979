import 'package:firebase_auth/firebase_auth.dart' as auth;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import '../theme/theme.dart';
import '../services/firestore.dart';
import '../models/models.dart';

class PrivacySettingsPage extends StatefulWidget {
  const PrivacySettingsPage({super.key});

  @override
  State<PrivacySettingsPage> createState() => _PrivacySettingsPageState();
}

class _PrivacySettingsPageState extends State<PrivacySettingsPage> {
  bool _isLoading = true;
  bool _isSaving = false;
  String? _error;

  User? _currentUser;
  UserProfile? _currentUserProfile;
  UserProfile? _modifiedUserProfile; // Track modifications
  List<SystemPersona> _availablePersonas = [];

  // Form controllers
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  List<String> _selectedPersonaIds = [];

  // Form key for validation
  final _formKey = GlobalKey<FormState>();

  // State tracking for modifications
  bool _hasUserChanges = false;
  bool _hasProfileChanges = false;

  // Expansion state for sections
  bool _basicInfoExpanded = true;
  bool _extendedProfileExpanded = true;
  bool _goalsExpanded = false;
  bool _factsExpanded = false;
  bool _familyExpanded = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final firebaseUser = auth.FirebaseAuth.instance.currentUser;
      if (firebaseUser == null) {
        throw Exception('User not authenticated');
      }

      // Load User data
      final user = await FirestoreService.getUser(firebaseUser.uid);

      // Load UserProfile data
      final userProfile = await FirestoreService.getUserProfile(
        firebaseUser.uid,
      );

      // Load available personas for selection
      final personas = await FirestoreService.getActiveSystemPersonas();

      setState(() {
        _currentUser = user;
        _currentUserProfile = userProfile;
        _modifiedUserProfile = userProfile; // Initialize with current data
        _availablePersonas = personas;

        // Initialize form controllers
        _nameController.text = user?.name ?? '';
        _descriptionController.text = user?.description ?? '';
        _selectedPersonaIds = List.from(user?.preferredPersonaIds ?? []);

        // Reset change tracking
        _hasUserChanges = false;
        _hasProfileChanges = false;

        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _saveUserData() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final firebaseUser = auth.FirebaseAuth.instance.currentUser;
      if (firebaseUser == null) {
        throw Exception('User not authenticated');
      }

      final trimmedName = _nameController.text.trim();
      final trimmedDescription = _descriptionController.text.trim();

      // Update user data atomically across all three stores
      await FirestoreService.updateUserOnboarding(
        userId: firebaseUser.uid,
        name: trimmedName,
        description: trimmedDescription.isEmpty ? null : trimmedDescription,
        preferredPersonaIds: _selectedPersonaIds,
      );

      // Reload data to reflect changes
      await _loadUserData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Profile updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update profile: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  Future<void> _deleteAllUserData() async {
    // Show confirmation dialog
    final confirmed = await _showDeleteConfirmationDialog();
    if (!confirmed) return;

    setState(() {
      _isSaving = true;
    });

    try {
      final firebaseUser = auth.FirebaseAuth.instance.currentUser;
      if (firebaseUser == null) {
        throw Exception('User not authenticated');
      }

      // Reset UserProfile to default state
      if (_currentUserProfile != null) {
        final defaultProfile = UserProfile(
          userId: firebaseUser.uid,
          name: null,
          age: null,
          gender: null,
          familyStatus: null,
          family: null,
          location: null,
          facts: null,
          likes: null,
          dislikes: null,
          preferences: null,
          goals: null,
          personalityTraits: null,
          interactionHistory: InteractionHistory(
            lastUpdated: DateTime.now(),
            sources: null,
          ),
        );

        await FirestoreService.createOrUpdateUserProfile(defaultProfile);
      }

      // Reset User document fields
      await FirestoreService.updateUserOnboarding(
        userId: firebaseUser.uid,
        name: 'User', // Default name
        description: null,
        preferredPersonaIds: [],
      );

      // Reload data to reflect changes
      await _loadUserData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('All user data has been deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete user data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  Future<void> _deleteAccount() async {
    // Show confirmation dialog
    final confirmed = await _showDeleteAccountConfirmationDialog();
    if (!confirmed) return;

    setState(() {
      _isSaving = true;
      _error = null;
    });

    try {
      final firebaseUser = auth.FirebaseAuth.instance.currentUser;
      if (firebaseUser == null) {
        throw Exception('User not authenticated');
      }

      final userId = firebaseUser.uid;

      // Step 1: Delete all user-owned data from Firestore
      await _deleteAllUserFirestoreData(userId);

      // Step 2: Delete the Firebase Authentication account
      await firebaseUser.delete();

      // Step 3: Navigate to login page
      if (mounted) {
        Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
      }
    } catch (e) {
      setState(() {
        _error = 'Failed to delete account: ${e.toString()}';
        _isSaving = false;
      });
    }
  }

  Future<void> _deleteAllUserFirestoreData(String userId) async {
    try {
      // Get all user's chats to delete messages
      final chats = await FirestoreService.getChats(userId);

      // Delete all messages in each chat
      for (final chat in chats) {
        if (chat.id != null) {
          final messages = await FirestoreService.getMessages(chat.id!, userId);

          // Delete messages in batches
          final batch = FirebaseFirestore.instance.batch();
          for (final message in messages) {
            if (message.id != null) {
              batch.delete(
                FirebaseFirestore.instance
                    .collection('chats')
                    .doc(chat.id!)
                    .collection('messages')
                    .doc(message.id!),
              );
            }
          }
          await batch.commit();

          // Delete the chat document
          await FirebaseFirestore.instance
              .collection('chats')
              .doc(chat.id!)
              .delete();
        }
      }

      // Delete user path progress
      final userPathProgress = await FirestoreService.getUserAllPathProgress(
        userId,
      );
      final progressBatch = FirebaseFirestore.instance.batch();
      for (final progress in userPathProgress) {
        if (progress.id != null) {
          progressBatch.delete(
            FirebaseFirestore.instance
                .collection('userPathProgress')
                .doc(progress.id!),
          );
        }
      }
      await progressBatch.commit();

      // Delete user profile
      await FirebaseFirestore.instance
          .collection('userProfiles')
          .doc(userId)
          .delete();

      // Delete user document
      await FirebaseFirestore.instance.collection('users').doc(userId).delete();
    } catch (e) {
      throw Exception('Failed to delete user data from Firestore: $e');
    }
  }

  Future<bool> _showDeleteConfirmationDialog() async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Delete All Data'),
              content: const Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'This action will permanently delete all your personal data including:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 12),
                  Text('• Personal information (age, location, gender, etc.)'),
                  Text('• Preferences and personality traits'),
                  Text('• Goals and achievements'),
                  Text('• Chat history insights'),
                  Text('• All profile customizations'),
                  SizedBox(height: 16),
                  Text(
                    'This action cannot be undone. Are you sure you want to continue?',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Delete All Data'),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  Future<bool> _showDeleteAccountConfirmationDialog() async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Row(
                children: [
                  Icon(
                    Icons.warning_amber_rounded,
                    color: Theme.of(context).colorScheme.error,
                    size: 24,
                  ),
                  SizedBox(width: AppDimensions.spacingS),
                  const Text('Delete Account'),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'This action will permanently delete your entire account and ALL associated data including:',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: AppDimensions.spacingM),
                  Text(
                    '• All chat history and messages',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  Text(
                    '• Guided path progress data',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  Text(
                    '• Complete user profile information',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  Text(
                    '• Account authentication data',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  Text(
                    '• All preferences and settings',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  SizedBox(height: AppDimensions.spacingL),
                  Container(
                    padding: AppDimensions.paddingS,
                    decoration: BoxDecoration(
                      color: Theme.of(
                        context,
                      ).colorScheme.errorContainer.withAlpha(102), // 0.4 * 255
                      borderRadius: AppDimensions.borderRadiusS,
                    ),
                    child: Text(
                      'WARNING: This action is irreversible and cannot be undone. Your data cannot be restored after deletion.',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.error,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  style: TextButton.styleFrom(
                    foregroundColor: Theme.of(context).colorScheme.primary,
                  ),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.error,
                    foregroundColor: Theme.of(context).colorScheme.onError,
                  ),
                  child: const Text(
                    'I understand and want to delete my account',
                  ),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  @override
  Widget build(BuildContext context) {
    final hasAnyChanges = _hasUserChanges || _hasProfileChanges;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Privacy & Data Settings'),
        actions: [
          if (hasAnyChanges && !_isLoading && !_isSaving)
            IconButton(
              icon: Icon(Icons.circle, size: 12, color: Colors.orange),
              onPressed: null,
              tooltip: 'Unsaved Changes',
            ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: hasAnyChanges && !_isLoading && !_isSaving
          ? FloatingActionButton.extended(
              onPressed: _saveAllChanges,
              icon: const Icon(Icons.save_alt),
              label: const Text('Save All'),
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
            )
          : null,
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading your data...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text('Error', style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 16),
            Container(
              padding: AppDimensions.paddingM,
              margin: AppDimensions.paddingM,
              decoration: BoxDecoration(
                color: Theme.of(
                  context,
                ).colorScheme.errorContainer.withAlpha(51), // 0.2 * 255
                borderRadius: AppDimensions.borderRadiusM,
                border: Border.all(
                  color: Theme.of(
                    context,
                  ).colorScheme.error.withAlpha(102), // 0.4 * 255
                ),
              ),
              child: SelectableText.rich(
                TextSpan(
                  children: [
                    WidgetSpan(
                      child: Icon(
                        Icons.error_outline,
                        color: Theme.of(context).colorScheme.error,
                        size: 16,
                      ),
                    ),
                    const TextSpan(text: ' '),
                    TextSpan(
                      text: _error!,
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.error,
                      ),
                    ),
                  ],
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadUserData,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: AppDimensions.paddingM,
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCurrentDataSection(),
            SizedBox(height: AppDimensions.spacingXl),
            _buildEditableDataSection(),
            SizedBox(height: AppDimensions.spacingXl),
            _buildDataDeletionSection(),
            SizedBox(height: AppDimensions.spacingXxl), // Extra space for FAB
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentDataSection() {
    return Column(
      children: [
        // Basic Information Section
        Card(
          child: ExpansionTile(
            initiallyExpanded: _basicInfoExpanded,
            onExpansionChanged: (expanded) {
              setState(() {
                _basicInfoExpanded = expanded;
              });
            },
            leading: Icon(
              AppIcons.info,
              color: Theme.of(context).colorScheme.primary,
            ),
            title: Text(
              'Basic Information',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            children: [
              Padding(
                padding: AppDimensions.paddingM,
                child: Column(
                  children: [
                    _buildDataItem(
                      'User ID',
                      _currentUser?.id ?? 'Not available',
                    ),
                    _buildDataItem(
                      'Email',
                      _currentUser?.email ?? 'Not available',
                    ),
                    _buildDataItem('Name', _currentUser?.name ?? 'Not set'),
                    _buildDataItem(
                      'Description',
                      _currentUser?.description ?? 'Not set',
                    ),
                    _buildDataItem(
                      'Onboarded',
                      _currentUser?.isOnboarded == true ? 'Yes' : 'No',
                    ),
                    _buildDataItem(
                      'Admin',
                      _currentUser?.isAdmin == true ? 'Yes' : 'No',
                    ),
                    _buildDataItem(
                      'Account Created',
                      _formatDate(_currentUser?.createdAt),
                    ),
                    _buildDataItem('Preferred Personas', _formatPersonaList()),
                  ],
                ),
              ),
            ],
          ),
        ),

        SizedBox(height: AppDimensions.spacingL),

        // Extended Profile Section
        if (_currentUserProfile != null) ...[
          Card(
            child: ExpansionTile(
              initiallyExpanded: _extendedProfileExpanded,
              onExpansionChanged: (expanded) {
                setState(() {
                  _extendedProfileExpanded = expanded;
                });
              },
              leading: Icon(
                AppIcons.user,
                color: Theme.of(context).colorScheme.primary,
              ),
              title: Text(
                'Extended Profile Data',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              children: [
                Padding(
                  padding: AppDimensions.paddingM,
                  child: Column(
                    children: [
                      _buildDataItem(
                        'Profile Name',
                        _currentUserProfile?.name ?? 'Not set',
                      ),
                      _buildDataItem(
                        'Age',
                        _currentUserProfile?.age?.toString() ?? 'Not set',
                      ),
                      _buildDataItem(
                        'Gender',
                        _currentUserProfile?.gender ?? 'Not set',
                      ),
                      _buildDataItem(
                        'Family Status',
                        _currentUserProfile?.familyStatus ?? 'Not set',
                      ),
                      _buildDataItem('Location', _formatLocation()),
                      _buildDataItem(
                        'Likes',
                        _formatStringList(_currentUserProfile?.likes),
                      ),
                      _buildDataItem(
                        'Dislikes',
                        _formatStringList(_currentUserProfile?.dislikes),
                      ),
                      _buildDataItem(
                        'Personality Traits',
                        _formatStringList(
                          _currentUserProfile?.personalityTraits,
                        ),
                      ),
                      _buildDataItem(
                        'Last Profile Update',
                        _formatDate(
                          _currentUserProfile?.interactionHistory.lastUpdated,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: AppDimensions.spacingL),

          // Goals Section
          _buildGoalsSection(),

          SizedBox(height: AppDimensions.spacingL),

          // Facts Section
          _buildFactsSection(),

          SizedBox(height: AppDimensions.spacingL),

          // Family Section
          _buildFamilySection(),
        ],
      ],
    );
  }

  Widget _buildDataItem(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: AppDimensions.spacingXs),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value, style: Theme.of(context).textTheme.bodyMedium),
          ),
        ],
      ),
    );
  }

  Widget _buildEditableDataSection() {
    return Column(
      children: [
        // Basic User Information
        Card(
          child: Padding(
            padding: AppDimensions.paddingM,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      AppIcons.edit,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    SizedBox(width: AppDimensions.spacingS),
                    Text(
                      'Edit Basic Information',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    if (_hasUserChanges) ...[
                      SizedBox(width: AppDimensions.spacingS),
                      Icon(Icons.circle, size: 8, color: Colors.orange),
                    ],
                  ],
                ),
                SizedBox(height: AppDimensions.spacingL),
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'Display Name',
                    hintText: 'Enter your display name',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.person),
                  ),
                  textCapitalization: TextCapitalization.words,
                  maxLength: 50,
                  enabled: !_isSaving,
                  onChanged: (value) => _markUserDataChanged(),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Name cannot be empty';
                    }
                    return null;
                  },
                ),
                SizedBox(height: AppDimensions.spacingM),
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description (Optional)',
                    hintText: 'Tell us a bit about yourself',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.description),
                  ),
                  maxLines: 3,
                  maxLength: 200,
                  enabled: !_isSaving,
                  onChanged: (value) => _markUserDataChanged(),
                ),
                SizedBox(height: AppDimensions.spacingL),
                Text(
                  'Preferred Coaching Personas',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                SizedBox(height: AppDimensions.spacingS),
                Text(
                  'Select the coaching personas you prefer to interact with:',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withAlpha(179), // 0.7 * 255
                  ),
                ),
                SizedBox(height: AppDimensions.spacingS),
                _buildPersonaSelection(),
                SizedBox(height: AppDimensions.spacingL),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: (_isSaving || !_hasUserChanges)
                        ? null
                        : _saveUserData,
                    icon: _isSaving
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.save),
                    label: Text(_isSaving ? 'Saving...' : 'Save Basic Changes'),
                    style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.symmetric(
                        vertical: AppDimensions.spacingM,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        SizedBox(height: AppDimensions.spacingL),

        // Extended Profile Editing Section
        if (_currentUserProfile != null) _buildExtendedProfileEditSection(),
      ],
    );
  }

  Widget _buildPersonaSelection() {
    if (_availablePersonas.isEmpty) {
      return const Text('No personas available');
    }

    return Wrap(
      spacing: AppDimensions.spacingS,
      runSpacing: AppDimensions.spacingS,
      children: _availablePersonas.map((persona) {
        final isSelected = _selectedPersonaIds.contains(persona.id);
        return FilterChip(
          label: Text(persona.name),
          selected: isSelected,
          onSelected: _isSaving
              ? null
              : (selected) {
                  setState(() {
                    if (selected) {
                      _selectedPersonaIds.add(persona.id!);
                    } else {
                      _selectedPersonaIds.remove(persona.id);
                    }
                    _markUserDataChanged();
                  });
                },
          avatar: isSelected ? const Icon(Icons.check, size: 16) : null,
        );
      }).toList(),
    );
  }

  Widget _buildDataDeletionSection() {
    return Card(
      child: Padding(
        padding: AppDimensions.paddingM,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.delete_forever,
                  color: Theme.of(context).colorScheme.error,
                ),
                SizedBox(width: AppDimensions.spacingS),
                Text(
                  'Data Deletion',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Theme.of(context).colorScheme.error,
                  ),
                ),
              ],
            ),
            SizedBox(height: AppDimensions.spacingM),

            // Delete Personal Data Section
            Text(
              'Delete All Personal Data',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              'This will permanently delete all your personal information, preferences, goals, and chat insights. Your account will remain active but all personalization will be lost.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            SizedBox(height: AppDimensions.spacingM),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isSaving ? null : _deleteAllUserData,
                icon: _isSaving
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.delete_forever),
                label: Text(_isSaving ? 'Deleting...' : 'Delete All My Data'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.errorContainer,
                  foregroundColor: Theme.of(
                    context,
                  ).colorScheme.onErrorContainer,
                  padding: EdgeInsets.symmetric(
                    vertical: AppDimensions.spacingM,
                  ),
                ),
              ),
            ),

            SizedBox(height: AppDimensions.spacingXl),

            // Delete Account Section
            Container(
              padding: AppDimensions.paddingM,
              decoration: BoxDecoration(
                color: Theme.of(
                  context,
                ).colorScheme.errorContainer.withAlpha(51), // 0.2 * 255
                borderRadius: AppDimensions.borderRadiusM,
                border: Border.all(
                  color: Theme.of(
                    context,
                  ).colorScheme.error.withAlpha(102), // 0.4 * 255
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.warning_amber_rounded,
                        color: Theme.of(context).colorScheme.error,
                        size: 20,
                      ),
                      SizedBox(width: AppDimensions.spacingS),
                      Text(
                        'Delete Account Permanently',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              color: Theme.of(context).colorScheme.error,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                    ],
                  ),
                  SizedBox(height: AppDimensions.spacingS),
                  Text(
                    'This will permanently delete your entire account and all associated data. This action cannot be undone and you will lose access to all your chats, progress, and account information.',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onErrorContainer,
                    ),
                  ),
                  SizedBox(height: AppDimensions.spacingM),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _isSaving ? null : _deleteAccount,
                      icon: _isSaving
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.delete_forever),
                      label: Text(
                        _isSaving ? 'Deleting Account...' : 'Delete My Account',
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.error,
                        foregroundColor: Theme.of(context).colorScheme.onError,
                        padding: EdgeInsets.symmetric(
                          vertical: AppDimensions.spacingM,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // State management methods
  void _markUserDataChanged() {
    if (!_hasUserChanges) {
      setState(() {
        _hasUserChanges = true;
      });
    }
  }

  void _markProfileDataChanged() {
    if (!_hasProfileChanges) {
      setState(() {
        _hasProfileChanges = true;
      });
    }
  }

  Widget _buildExtendedProfileEditSection() {
    return Card(
      child: Padding(
        padding: AppDimensions.paddingM,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.person_outline,
                  color: Theme.of(context).colorScheme.primary,
                ),
                SizedBox(width: AppDimensions.spacingS),
                Text(
                  'Edit Extended Profile',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                if (_hasProfileChanges) ...[
                  SizedBox(width: AppDimensions.spacingS),
                  Icon(Icons.circle, size: 8, color: Colors.orange),
                ],
              ],
            ),
            SizedBox(height: AppDimensions.spacingL),

            // Age field
            TextFormField(
              initialValue: _modifiedUserProfile?.age?.toString() ?? '',
              decoration: const InputDecoration(
                labelText: 'Age',
                hintText: 'Enter your age',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.cake),
              ),
              keyboardType: TextInputType.number,
              enabled: !_isSaving,
              onChanged: (value) {
                _markProfileDataChanged();
                final age = int.tryParse(value);
                if (_modifiedUserProfile != null) {
                  _modifiedUserProfile = _modifiedUserProfile!.copyWith(
                    age: age,
                  );
                }
              },
            ),

            SizedBox(height: AppDimensions.spacingM),

            // Gender dropdown
            DropdownButtonFormField<String>(
              initialValue: _modifiedUserProfile?.gender,
              decoration: const InputDecoration(
                labelText: 'Gender',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person),
              ),
              items: const [
                DropdownMenuItem(value: null, child: Text('Not specified')),
                DropdownMenuItem(value: 'male', child: Text('Male')),
                DropdownMenuItem(value: 'female', child: Text('Female')),
                DropdownMenuItem(
                  value: 'non-binary',
                  child: Text('Non-binary'),
                ),
                DropdownMenuItem(value: 'other', child: Text('Other')),
                DropdownMenuItem(
                  value: 'unspecified',
                  child: Text('Prefer not to say'),
                ),
              ],
              onChanged: _isSaving
                  ? null
                  : (value) {
                      _markProfileDataChanged();
                      if (_modifiedUserProfile != null) {
                        _modifiedUserProfile = _modifiedUserProfile!.copyWith(
                          gender: value,
                        );
                      }
                    },
            ),

            SizedBox(height: AppDimensions.spacingM),

            // Family Status dropdown
            DropdownButtonFormField<String>(
              initialValue: _modifiedUserProfile?.familyStatus,
              decoration: const InputDecoration(
                labelText: 'Family Status',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.family_restroom),
              ),
              items: const [
                DropdownMenuItem(value: null, child: Text('Not specified')),
                DropdownMenuItem(value: 'single', child: Text('Single')),
                DropdownMenuItem(value: 'married', child: Text('Married')),
                DropdownMenuItem(value: 'partnered', child: Text('Partnered')),
                DropdownMenuItem(value: 'divorced', child: Text('Divorced')),
                DropdownMenuItem(value: 'widowed', child: Text('Widowed')),
                DropdownMenuItem(
                  value: 'unspecified',
                  child: Text('Prefer not to say'),
                ),
              ],
              onChanged: _isSaving
                  ? null
                  : (value) {
                      _markProfileDataChanged();
                      if (_modifiedUserProfile != null) {
                        _modifiedUserProfile = _modifiedUserProfile!.copyWith(
                          familyStatus: value,
                        );
                      }
                    },
            ),

            SizedBox(height: AppDimensions.spacingL),

            // Save button for profile changes
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: (_isSaving || !_hasProfileChanges)
                    ? null
                    : _saveProfileData,
                icon: _isSaving
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.save),
                label: Text(_isSaving ? 'Saving...' : 'Save Profile Changes'),
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(
                    vertical: AppDimensions.spacingM,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveProfileData() async {
    if (_modifiedUserProfile == null) return;

    setState(() {
      _isSaving = true;
    });

    try {
      await FirestoreService.createOrUpdateUserProfile(_modifiedUserProfile!);

      setState(() {
        _currentUserProfile = _modifiedUserProfile;
        _hasProfileChanges = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Profile updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update profile: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  Future<void> _saveAllChanges() async {
    setState(() {
      _isSaving = true;
    });

    try {
      // Save user data if changed
      if (_hasUserChanges) {
        final trimmedName = _nameController.text.trim();
        final trimmedDescription = _descriptionController.text.trim();

        await FirestoreService.updateUserOnboarding(
          userId: auth.FirebaseAuth.instance.currentUser!.uid,
          name: trimmedName,
          description: trimmedDescription.isEmpty ? null : trimmedDescription,
          preferredPersonaIds: _selectedPersonaIds,
        );
      }

      // Save profile data if changed
      if (_hasProfileChanges && _modifiedUserProfile != null) {
        await FirestoreService.createOrUpdateUserProfile(_modifiedUserProfile!);
      }

      // Reload data to reflect changes
      await _loadUserData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('All changes saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save changes: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  // Helper methods for formatting data
  String _formatDate(DateTime? date) {
    if (date == null) return 'Not available';
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  String _formatPersonaList() {
    if (_currentUser?.preferredPersonaIds.isEmpty ?? true) {
      return 'None selected';
    }

    final personaNames = _currentUser!.preferredPersonaIds
        .map(
          (id) => _availablePersonas
              .firstWhere(
                (persona) => persona.id == id,
                orElse: () => SystemPersona(
                  id: id,
                  name: 'Unknown ($id)',
                  title: 'Unknown',
                  avatarUrl: 'assets/persona-profile-icon.jpg',
                  description: 'Unknown persona',
                  isActive: false,
                  defaultOrder: 999,
                  approach: 'Unknown',
                  coachingStyle: 'Unknown',
                  specialties: ['Unknown'],
                  videoUrl: '',
                  catchphrase: 'Unknown persona',
                ),
              )
              .name,
        )
        .toList();

    return personaNames.join(', ');
  }

  String _formatLocation() {
    final location = _currentUserProfile?.location;
    if (location == null) return 'Not set';

    final parts = <String>[];
    if (location.town != null && location.town!.isNotEmpty) {
      parts.add(location.town!);
    }
    if (location.country.isNotEmpty) {
      parts.add(location.country);
    }

    return parts.isEmpty ? 'Not set' : parts.join(', ');
  }

  String _formatStringList(List<String>? list) {
    if (list == null || list.isEmpty) return 'None';
    return list.join(', ');
  }

  /// Safely converts a fact value (dynamic type) to a String for display
  /// Handles null, String, int, bool, and other types appropriately
  String _formatFactValue(dynamic value) {
    if (value == null) {
      return 'Not set';
    }

    if (value is String) {
      return value.isEmpty ? 'Empty' : value;
    }

    if (value is int || value is double) {
      return value.toString();
    }

    if (value is bool) {
      return value ? 'Yes' : 'No';
    }

    // For any other type, convert to string
    return value.toString();
  }

  Widget _buildGoalsSection() {
    final goals = _currentUserProfile?.goals ?? [];

    return Card(
      child: ExpansionTile(
        initiallyExpanded: _goalsExpanded,
        onExpansionChanged: (expanded) {
          setState(() {
            _goalsExpanded = expanded;
          });
        },
        leading: Icon(
          Icons.flag_outlined,
          color: Theme.of(context).colorScheme.primary,
        ),
        title: Text(
          'Goals (${goals.length})',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        children: [
          if (goals.isEmpty)
            Padding(
              padding: AppDimensions.paddingM,
              child: Text(
                'No goals set',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withAlpha(153), // 0.6 * 255
                ),
              ),
            )
          else
            ...goals.map((goal) => _buildGoalItem(goal)),
        ],
      ),
    );
  }

  Widget _buildGoalItem(Goal goal) {
    return Card(
      margin: EdgeInsets.symmetric(
        horizontal: AppDimensions.spacingM,
        vertical: AppDimensions.spacingS,
      ),
      child: ListTile(
        title: Text(
          goal.description,
          style: Theme.of(context).textTheme.bodyLarge,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: AppDimensions.spacingXs),
            Row(
              children: [
                Icon(
                  _getStatusIcon(goal.status),
                  size: 16,
                  color: _getStatusColor(goal.status),
                ),
                SizedBox(width: AppDimensions.spacingXs),
                Text(
                  goal.status.replaceAll('_', ' ').toUpperCase(),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: _getStatusColor(goal.status),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            SizedBox(height: AppDimensions.spacingXs),
            Text(
              'Created: ${_formatDate(goal.createdAt)}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            if (goal.updatedAt != null)
              Text(
                'Updated: ${_formatDate(goal.updatedAt)}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit, size: 20),
              onPressed: () => _editGoal(goal),
              tooltip: 'Edit Goal',
            ),
            IconButton(
              icon: const Icon(Icons.delete, size: 20),
              onPressed: () => _deleteGoal(goal),
              tooltip: 'Delete Goal',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFactsSection() {
    final facts = _currentUserProfile?.facts ?? [];

    return Card(
      child: ExpansionTile(
        initiallyExpanded: _factsExpanded,
        onExpansionChanged: (expanded) {
          setState(() {
            _factsExpanded = expanded;
          });
        },
        leading: Icon(
          Icons.lightbulb_outline,
          color: Theme.of(context).colorScheme.primary,
        ),
        title: Text(
          'Facts (${facts.length})',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        children: [
          if (facts.isEmpty)
            Padding(
              padding: AppDimensions.paddingM,
              child: Text(
                'No facts recorded',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withAlpha(153), // 0.6 * 255
                ),
              ),
            )
          else
            ...facts.map((fact) => _buildFactItem(fact)),
        ],
      ),
    );
  }

  Widget _buildFactItem(Fact fact) {
    return Card(
      margin: EdgeInsets.symmetric(
        horizontal: AppDimensions.spacingM,
        vertical: AppDimensions.spacingS,
      ),
      child: ListTile(
        title: Text(
          fact.key,
          style: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w500),
        ),
        subtitle: Text(
          _formatFactValue(fact.value),
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit, size: 20),
              onPressed: () => _editFact(fact),
              tooltip: 'Edit Fact',
            ),
            IconButton(
              icon: const Icon(Icons.delete, size: 20),
              onPressed: () => _deleteFact(fact),
              tooltip: 'Delete Fact',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFamilySection() {
    final family = _currentUserProfile?.family ?? [];

    return Card(
      child: ExpansionTile(
        initiallyExpanded: _familyExpanded,
        onExpansionChanged: (expanded) {
          setState(() {
            _familyExpanded = expanded;
          });
        },
        leading: Icon(
          Icons.family_restroom,
          color: Theme.of(context).colorScheme.primary,
        ),
        title: Text(
          'Family Members (${family.length})',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        children: [
          if (family.isEmpty)
            Padding(
              padding: AppDimensions.paddingM,
              child: Text(
                'No family members added',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withAlpha(153), // 0.6 * 255
                ),
              ),
            )
          else
            ...family.map((member) => _buildFamilyMemberItem(member)),
        ],
      ),
    );
  }

  Widget _buildFamilyMemberItem(RelationInfo member) {
    return Card(
      margin: EdgeInsets.symmetric(
        horizontal: AppDimensions.spacingM,
        vertical: AppDimensions.spacingS,
      ),
      child: ListTile(
        title: Text(
          member.name,
          style: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w500),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${member.relation.toUpperCase()}, Age: ${member.age}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            if (member.otherInfo != null && member.otherInfo!.isNotEmpty)
              Text(
                member.otherInfo!.join(', '),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withAlpha(179), // 0.7 * 255
                ),
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit, size: 20),
              onPressed: () => _editFamilyMember(member),
              tooltip: 'Edit Family Member',
            ),
            IconButton(
              icon: const Icon(Icons.delete, size: 20),
              onPressed: () => _deleteFamilyMember(member),
              tooltip: 'Delete Family Member',
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods for status display
  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'not_started':
        return Icons.radio_button_unchecked;
      case 'in_progress':
        return Icons.pending;
      case 'achieved':
        return Icons.check_circle;
      case 'abandoned':
        return Icons.cancel;
      default:
        return Icons.help_outline;
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'not_started':
        return Theme.of(
          context,
        ).colorScheme.onSurface.withAlpha(153); // 0.6 * 255
      case 'in_progress':
        return Colors.orange;
      case 'achieved':
        return Colors.green;
      case 'abandoned':
        return Colors.red;
      default:
        return Theme.of(context).colorScheme.onSurface;
    }
  }

  // Edit/Delete methods (placeholder implementations)
  void _editGoal(Goal goal) {
    // TODO: Implement goal editing dialog
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Goal editing coming soon')));
  }

  void _deleteGoal(Goal goal) {
    // TODO: Implement goal deletion
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Goal deletion coming soon')));
  }

  void _editFact(Fact fact) {
    // TODO: Implement fact editing dialog
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Fact editing coming soon')));
  }

  void _deleteFact(Fact fact) {
    // TODO: Implement fact deletion
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Fact deletion coming soon')));
  }

  void _editFamilyMember(RelationInfo member) {
    // TODO: Implement family member editing dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Family member editing coming soon')),
    );
  }

  void _deleteFamilyMember(RelationInfo member) {
    // TODO: Implement family member deletion
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Family member deletion coming soon')),
    );
  }
}
