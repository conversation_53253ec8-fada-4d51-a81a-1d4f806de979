import 'dart:convert';
import 'package:flutter/services.dart';
import '../../models/models.dart';
import '../../services/firestore.dart';

/// Utility class for seeding ChatSubject entities from JSON file
/// Reads data from data/chat_subjects.json
class ChatSubjectSeeder {
  /// Loads and parses the ChatSubject data from JSON file
  static Future<List<Map<String, dynamic>>> _loadSubjectsFromJson() async {
    try {
      final String jsonString = await rootBundle.loadString(
        'data/chat_subjects.json',
      );
      final List<dynamic> jsonData = jsonDecode(jsonString);
      return jsonData.cast<Map<String, dynamic>>();
    } catch (e) {
      throw Exception('Failed to load chat subjects from JSON: $e');
    }
  }

  /// Returns the list of ChatSubject entities loaded from JSON
  static Future<List<ChatSubject>> getDefaultChatSubjects() async {
    try {
      final jsonData = await _loadSubjectsFromJson();
      final subjects = <ChatSubject>[];

      for (final subjectData in jsonData) {
        try {
          final subject = ChatSubject.fromJson(subjectData);
          subjects.add(subject);
        } catch (e) {
          throw Exception('Failed to parse chat subject data: $e');
        }
      }

      return subjects;
    } catch (e) {
      throw Exception('Failed to get default chat subjects: $e');
    }
  }

  /// Seeds the ChatSubject entities from JSON into Firestore
  /// Returns the list of created document IDs
  static Future<List<String>> seedDefaultChatSubjects() async {
    final subjects = await getDefaultChatSubjects();
    return await FirestoreService.createChatSubjects(subjects);
  }

  /// Checks if ChatSubject entities already exist in Firestore
  static Future<bool> subjectsExist() async {
    final count = await FirestoreService.getChatSubjectCount();
    return count > 0;
  }

  /// Seeds chat subjects only if they don't already exist
  /// Returns created document IDs, or empty list if they already exist
  static Future<List<String>> seedIfEmpty() async {
    if (await subjectsExist()) {
      return [];
    }
    return await seedDefaultChatSubjects();
  }

  /// Gets the count of chat subjects that would be seeded from JSON
  static Future<int> getSubjectCount() async {
    final subjects = await getDefaultChatSubjects();
    return subjects.length;
  }

  /// Validates that all subjects in JSON have required fields
  static Future<bool> validateSubjectData() async {
    try {
      final subjects = await getDefaultChatSubjects();

      for (final s in subjects) {
        if (s.name.isEmpty) {
          throw Exception('ChatSubject missing required field: name');
        }
        if (s.description.isEmpty) {
          throw Exception(
            'ChatSubject "${s.name}" missing required field: description',
          );
        }
        if (s.prompt.isEmpty) {
          throw Exception(
            'ChatSubject "${s.name}" missing required field: prompt',
          );
        }
        if (s.defaultOrder < 0) {
          throw Exception('ChatSubject "${s.name}" has negative defaultOrder');
        }
      }

      return true;
    } catch (e) {
      throw Exception('ChatSubject validation failed: $e');
    }
  }
}
