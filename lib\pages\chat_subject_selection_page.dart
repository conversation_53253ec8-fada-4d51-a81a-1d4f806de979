import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/models.dart';
import '../services/firestore.dart';
import '../theme/theme.dart';
import 'persona_selection_page.dart';
import '../widgets/chat_subject_chip.dart';

class ChatSubjectSelectionController extends ChangeNotifier {
  ChatSubjectSelectionController();

  List<ChatSubject> _subjects = [];
  bool _isLoading = false;
  String? _errorMessage;
  int? _selectedIndex;

  List<ChatSubject> get subjects => _subjects;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  int? get selectedIndex => _selectedIndex;
  ChatSubject? get selectedSubject =>
      _selectedIndex != null ? _subjects[_selectedIndex!] : null;

  Future<void> load() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    try {
      final list = await FirestoreService.getActiveChatSubjects();
      _subjects = list;
    } catch (e) {
      _errorMessage = 'Failed to load chat subjects: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void selectIndex(int? index) {
    _selectedIndex = index;
    notifyListeners();
  }
}

class ChatSubjectSelectionPage extends StatelessWidget {
  const ChatSubjectSelectionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => ChatSubjectSelectionController()..load(),
      child: const _ChatSubjectSelectionView(),
    );
  }
}

class _ChatSubjectSelectionView extends StatelessWidget {
  const _ChatSubjectSelectionView();

  @override
  Widget build(BuildContext context) {
    final controller = context.watch<ChatSubjectSelectionController>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Choose a Topic'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
      ),
      body: _buildBody(context, controller),
      bottomNavigationBar: controller.selectedIndex != null
          ? _buildBottomBar(context, controller)
          : null,
    );
  }

  Widget _buildBody(
    BuildContext context,
    ChatSubjectSelectionController controller,
  ) {
    if (controller.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (controller.errorMessage != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: SelectableText.rich(
            TextSpan(
              text: 'Error: ',
              style: TextStyle(
                color: context.colorScheme.error,
                fontWeight: FontWeight.bold,
              ),
              children: [
                TextSpan(
                  text: controller.errorMessage,
                  style: TextStyle(
                    color: context.colorScheme.error,
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ],
            ),
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    if (controller.subjects.isEmpty) {
      return const Center(child: Text('No topics available'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Pick a coaching topic to get started.',
            style: context.textTheme.bodyLarge?.copyWith(
              color: context.colorScheme.onSurface.withValues(alpha: 0.8),
            ),
          ),
          SizedBox(height: AppDimensions.spacingM),
          ...['daily', 'weekly', 'monthly'].expand((type) {
            final group = controller.subjects
                .where((s) => s.type == type)
                .toList();
            if (group.isEmpty) return <Widget>[];
            final titles = {
              'daily': 'Daily',
              'weekly': 'Weekly Planning',
              'monthly': 'Long term alignment',
            };
            return <Widget>[
              Padding(
                padding: const EdgeInsets.only(top: 8, bottom: 8),
                child: Text(titles[type]!, style: context.textTheme.titleLarge),
              ),
              ...group.map((subject) {
                final originalIndex = controller.subjects.indexOf(subject);
                final isSelected = controller.selectedIndex == originalIndex;
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: ChatSubjectCard(
                    title: subject.name,
                    description: subject.description,
                    isSelected: isSelected,
                    fullWidth: true,
                    onTap: () => controller.selectIndex(
                      isSelected ? null : originalIndex,
                    ),
                  ),
                );
              }),
            ];
          }),
        ],
      ),
    );
  }

  Widget _buildBottomBar(
    BuildContext context,
    ChatSubjectSelectionController controller,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () => _onContinue(context, controller),
            child: const Text('Continue'),
          ),
        ),
      ),
    );
  }

  void _onContinue(
    BuildContext context,
    ChatSubjectSelectionController controller,
  ) {
    final subject = controller.selectedSubject;
    if (subject == null) return;

    // Replace with persona selection, carrying the chosen subject forward
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => PersonaSelectionPage(chatSubject: subject),
      ),
    );
  }
}
