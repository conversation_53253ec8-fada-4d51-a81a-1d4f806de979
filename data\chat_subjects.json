[{"name": "Free Conversation", "description": "Have an open, unstructured coaching chat about anything on your mind.", "prompt": "Facilitate an open coaching chat. Clarify what the user wants to\n      explore and the outcome for this session. Use reflective listening,\n      Socratic questions, and a light GROW structure (Goal, Reality, Options,\n      Will). End with 1–3 specific next steps.", "isActive": true, "defaultOrder": 1, "type": "daily", "category": "General"}, {"name": "Daily Check-In", "description": "Quick reflection on your current state of mind, priorities, and focus for today.", "prompt": "Guide a 3–5 minute check-in. Establish today’s focus and energy.\n      Use GROW: Goal for today, Reality (constraints), Options (one small win),\n      Will (commitment). Keep it practical and positive; end with a clear focus\n      and a single next action.", "isActive": true, "defaultOrder": 2, "type": "daily", "category": "Focus & Productivity"}, {"name": "Evening Reflection", "description": "End the day by reviewing what went well and preparing for tomorrow.", "prompt": "Run a concise evening review. Prompt wins, lessons, obstacles.\n      Use Start/Stop/Continue and gratitude reframing. Extract one insight and\n      set 1–2 carry‑forward priorities for tomorrow. Keep tone calm and\n      encouraging.", "isActive": true, "defaultOrder": 3, "type": "daily", "category": "Mindset & Resilience"}, {"name": "Stress Reset", "description": "Transform today’s stress into clarity and calm energy.", "prompt": "Downshift stress into clarity. Use cognitive reframing and optional\n      box breathing. Separate facts from interpretations, identify triggers, and\n      co-create a small action or boundary. Keep language grounding and\n      compassionate.", "isActive": true, "defaultOrder": 4, "type": "daily", "category": "Mindset & Resilience"}, {"name": "Weekly Reset & Planning", "description": "Review the past week, reset your focus, and set clear priorities for the week ahead.", "prompt": "Lead a structured weekly review. Review goals, outcomes, lessons;\n      clarify priorities and plan 3–5 high‑impact tasks. Encourage\n      time‑blocking and constraints. Finish with a motivating intention for the\n      week.", "isActive": true, "defaultOrder": 5, "type": "weekly", "category": "Focus & Productivity"}, {"name": "Confidence Boost", "description": "Strengthen self-belief and inner resilience.", "prompt": "Coach toward self‑efficacy. Use evidence logging (past wins),\n      strengths spotting, and future pacing. Reframe self‑talk and design one\n      small mastery action. Keep momentum‑building and specific.", "isActive": true, "defaultOrder": 6, "type": "weekly", "category": "Mindset & Resilience"}, {"name": "Habit Builder", "description": "Design and strengthen a new positive habit.", "prompt": "Design or strengthen one habit. Use the Habit Loop and Tiny Habits.\n      Define trigger, minimum viable action, and celebration. Reduce friction\n      via environment design. End with a clear, trackable commitment.", "isActive": true, "defaultOrder": 7, "type": "weekly", "category": "Habit Formation"}, {"name": "Breaking Patterns", "description": "Identify and overcome limiting or destructive behaviors.", "prompt": "Identify and interrupt an unhelpful pattern. Map the chain\n      (cue→thought→emotion→behavior→result). Use If‑Then implementation\n      intentions and a replacement behavior. Plan one interruption experiment\n      and a reflection prompt.", "isActive": true, "defaultOrder": 8, "type": "weekly", "category": "Habit Formation"}, {"name": "Consistency Check", "description": "Review your progress and strengthen long-term discipline.", "prompt": "Assess consistency objectively. Review streaks, context, and\n      barriers without judgment. Use identity‑based framing and schedule tweaks.\n      Commit to a sustainable cadence for the next 7 days.", "isActive": true, "defaultOrder": 9, "type": "weekly", "category": "Habit Formation"}, {"name": "Purpose Discovery", "description": "Explore your authentic purpose and life direction.", "prompt": "Explore purpose with depth. Use values elicitation, peak\n      experiences, and contribution themes. Ask meaning‑oriented questions,\n      then synthesize into a short purpose draft and a small alignment\n      experiment.", "isActive": true, "defaultOrder": 9, "type": "monthly", "category": "Life Design"}, {"name": "Values Alignment", "description": "Check if your current choices align with your values.", "prompt": "Check alignment between actions and core values. Elicit top values,\n      review recent decisions, and score alignment. Identify one misalignment\n      and design a micro‑adjustment. Keep it empowering.", "isActive": true, "defaultOrder": 10, "type": "monthly", "category": "Life Design"}, {"name": "Life Vision Map", "description": "Create or refine your vision for an ideal future.", "prompt": "Map a compelling future. Use 3‑year future pacing and domains\n      (work, health, relationships, growth). Translate into 3 north‑star goals\n      and the first small step for each.", "isActive": true, "defaultOrder": 11, "type": "monthly", "category": "Life Design"}]